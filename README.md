# 8D问题分析报告生成系统

智能化8D报告生成工具，集成AI优化、版本管理、协作编辑功能，快速生成专业的8D分析报告。

## ✨ 核心特性

- 🤖 **AI智能优化**：基于少量输入生成完整报告，支持多轮对话式优化
- 📊 **版本管理**：支持多版本表单管理，可随时切换和对比历史版本
- 📄 **双格式输出**：一键生成PPT演示文稿和DOCX报告文档
- 🎨 **现代化界面**：响应式设计，支持桌面和移动设备
- 👥 **协作功能**：支持团队协作编辑和版本控制

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动服务
```bash
python app.py
```

### 访问系统
- Web界面: http://localhost:5555
- 填写表单 → AI优化 → 生成文档

## 📋 使用流程

1. **填写表单**：按8D步骤填写必填项（红色*标记）
2. **AI优化**：点击"AI优化"让AI完善内容
3. **生成报告**：一键导出PPT和DOCX文档
4. **版本管理**：支持多版本切换和历史回退

## 🛠️ 技术栈

- **后端**：Flask 3.0.2 + Python 3.7+
- **前端**：原生JavaScript + CSS3
- **文档处理**：python-pptx + python-docx
- **AI集成**：Dify API
- **数据存储**：localStorage + JSON日志

## 📖 详细文档

查看 [PROJECT_GUIDE.md](PROJECT_GUIDE.md) 获取完整的系统架构、功能说明和使用指南。

---

*智能化8D报告生成，让质量改进更高效、更专业！*

