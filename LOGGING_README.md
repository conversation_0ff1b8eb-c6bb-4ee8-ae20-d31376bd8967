# 日志系统说明

## 概述

本项目已经建立了完善的日志体系，替代了所有的print语句，提供了结构化的日志记录功能。

## 日志文件

- `logs/app.log` - 应用程序日志（INFO级别及以上）
- `logs/business.log` - 业务操作日志（JSON格式）
- `logs/error.log` - 错误日志（ERROR级别，JSON格式）

## 日志级别

- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息

## 日志功能

### 1. 自动请求日志
- 所有HTTP请求和响应都会自动记录
- 包含请求时间、响应时间、状态码等信息

### 2. 业务操作日志
- 8D报告提交
- AI生成报告
- 文件生成操作
- API调用记录

### 3. 错误日志
- 异常捕获和记录
- 详细的错误堆栈信息
- 错误上下文信息

### 4. 文件操作日志
- 模板文件加载
- 生成文件记录
- 文件删除操作

## 日志API

### 查看日志
```
GET /api/logs?type=app&lines=100
```

参数：
- `type`: 日志类型 (app, business, error)
- `lines`: 返回的行数 (默认100)

## 日志格式

### 普通日志格式
```
2025-01-27 10:30:45 - app - INFO - 收到请求: POST /submit_8d_report
```

### JSON格式（业务日志）
```json
{
  "timestamp": "2025-01-27T10:30:45.123456",
  "level": "INFO",
  "logger": "business",
  "message": "BUSINESS_OP: {...}",
  "operation": "submit_8d_report",
  "details": {
    "form_data_keys": ["D0汇报信息", "D1建立小组"],
    "has_ai_requirements": true
  },
  "request_info": {
    "ip_address": "127.0.0.1",
    "user_agent": "Mozilla/5.0...",
    "method": "POST",
    "url": "http://localhost:5555/submit_8d_report"
  }
}
```

## 日志配置

日志配置在 `logger_config.py` 文件中，包括：

- 日志文件轮转（10MB大小限制，保留5个备份）
- 不同级别的日志分别记录
- JSON格式的业务日志
- 控制台输出（开发环境）

## 使用示例

### 记录业务操作
```python
from logger_config import log_business_operation

log_business_operation('submit_form', {
    'form_type': '8d_report',
    'user_id': 'user123'
})
```

### 记录错误
```python
from logger_config import log_error_operation

try:
    # 业务逻辑
    pass
except Exception as e:
    log_error_operation('process_data', e, {'data_id': '123'})
```

### 记录API调用
```python
from logger_config import log_api_call

log_api_call('dify_chat', request_data, response_data)
```

## 日志清理

日志文件会自动轮转，超过10MB的文件会被压缩备份。系统会保留最近5个备份文件。

## 注意事项

1. 生产环境中建议关闭DEBUG级别的日志
2. 敏感信息不会记录在日志中
3. 日志文件使用UTF-8编码
4. 所有时间戳使用ISO格式 