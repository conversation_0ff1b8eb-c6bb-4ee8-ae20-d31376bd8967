import logging
import logging.handlers
import os
from datetime import datetime
import json
from flask import request, g
import traceback


# 确保日志目录存在
LOG_DIR = 'logs'
os.makedirs(LOG_DIR, exist_ok=True)

# 日志格式
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
JSON_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

class JSONFormatter(logging.Formatter):
    """JSON格式的日志格式化器"""
    
    def format(self, record):
        # 获取请求信息
        request_info = {}
        try:
            if hasattr(request, 'remote_addr'):
                request_info['ip_address'] = request.remote_addr
            if hasattr(request, 'method'):
                request_info['method'] = request.method
            if hasattr(request, 'url'):
                request_info['url'] = request.url
        except Exception:
            pass
        
        # 获取会话信息
        session_info = {}
        try:
            if hasattr(g, 'session_id'):
                session_info['session_id'] = g.session_id
            if hasattr(g, 'user_id'):
                session_info['user_id'] = g.user_id
        except Exception:
            pass
        
        # 构建日志记录
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'request_info': request_info,
            'session_info': session_info
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)

def setup_logger(name='app', level=logging.WARNING):  # 默认级别改为WARNING
    """设置日志记录器"""
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(LOG_FORMAT)
    json_formatter = JSONFormatter()
    
    # 应用日志处理器 - 只记录WARNING及以上级别
    app_handler = logging.handlers.RotatingFileHandler(
        os.path.join(LOG_DIR, 'app.log'),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    app_handler.setLevel(logging.WARNING)  # 改为WARNING
    app_handler.setFormatter(formatter)
    logger.addHandler(app_handler)
    
    # 业务日志处理器 - 只记录重要业务操作
    business_handler = logging.handlers.RotatingFileHandler(
        os.path.join(LOG_DIR, 'business.log'),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    business_handler.setLevel(logging.INFO)
    business_handler.setFormatter(json_formatter)
    logger.addHandler(business_handler)
    
    # 错误日志处理器
    error_handler = logging.handlers.RotatingFileHandler(
        os.path.join(LOG_DIR, 'error.log'),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(json_formatter)
    logger.addHandler(error_handler)
    
    # 控制台处理器（开发环境）- 只显示ERROR
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.ERROR)  # 改为ERROR
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

# 创建主日志记录器
logger = setup_logger('app')

def log_business_operation(operation, details=None, user_id=None, session_id=None):
    """记录重要业务操作"""
    # 只记录关键业务操作
    important_operations = {
        'submit_8d_report', 'generate_direct_report', 'ai_enhancement_success',
        'save_session_report', 'cleanup_expired_sessions'
    }
    
    if operation not in important_operations:
        return  # 跳过非关键操作
    
    extra_fields = {
        'operation': operation,
        'details': details or {},
        'user_id': user_id,
        'session_id': session_id
    }
    
    record = logger.makeRecord(
        'business', logging.INFO, '', 0, 
        f'BUSINESS_OP: {json.dumps(extra_fields, ensure_ascii=False)}', 
        (), None
    )
    record.extra_fields = extra_fields
    logger.handle(record)



def log_error_operation(operation, error, details=None, user_id=None, session_id=None):
    """记录错误操作"""
    extra_fields = {
        'operation': operation,
        'error': str(error),
        'details': details or {},
        'user_id': user_id,
        'session_id': session_id
    }
    
    record = logger.makeRecord(
        'business', logging.ERROR, '', 0, 
        f'ERROR_OP: {json.dumps(extra_fields, ensure_ascii=False)}', 
        (), None
    )
    record.extra_fields = extra_fields
    logger.handle(record)

def log_user_action(action, details=None, user_id=None, session_id=None):
    """记录关键用户操作"""
    # 只记录关键用户操作
    important_actions = {'http_request', 'http_response'}
    
    if action not in important_actions:
        return  # 跳过非关键操作
    
    extra_fields = {
        'action': action,
        'details': details or {},
        'user_id': user_id,
        'session_id': session_id
    }
    
    record = logger.makeRecord(
        'business', logging.INFO, '', 0, 
        f'USER_ACTION: {json.dumps(extra_fields, ensure_ascii=False)}', 
        (), None
    )
    record.extra_fields = extra_fields
    logger.handle(record)

def log_api_call(api_name, request_data=None, response_data=None, error=None):
    """记录API调用 - 只记录错误"""
    if not error:
        return  # 只记录API调用错误
    
    extra_fields = {
        'api_name': api_name,
        'request_data': request_data,
        'response_data': response_data,
        'error': str(error) if error else None
    }
    
    record = logger.makeRecord(
        'api', logging.ERROR, '', 0, 
        f'API_CALL_ERROR: {json.dumps(extra_fields, ensure_ascii=False)}', 
        (), None
    )
    record.extra_fields = extra_fields
    logger.handle(record)

def log_file_operation(operation, file_path, details=None):
    """记录文件操作 - 只记录错误"""
    # 文件操作通常不需要记录，除非出错
    pass 