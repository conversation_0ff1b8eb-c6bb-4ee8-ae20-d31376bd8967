from flask import Flask, request, jsonify, send_file, url_for, render_template, redirect, session, flash, g
from pptx import Presentation
from pptx.enum.text import PP_ALIGN
from docx import Document
import json
import os
import tempfile
from datetime import datetime, timedelta
import uuid
import pickle
import requests
from io import BytesIO
import zipfile
from logger_config import logger, log_business_operation, log_error_operation, log_user_action, log_api_call, log_file_operation


app = Flask(__name__)
app.secret_key = 'your-secret-key-for-session'  # 用于session加密

# 请求日志中间件
@app.before_request
def log_request_info():
    """记录请求信息"""
    g.start_time = datetime.now()
    g.request_id = str(uuid.uuid4())
    
    # 只记录关键请求（POST请求）
    if request.method == 'POST':
        request_info = {
            'method': request.method,
            'url': request.url,
            'ip': request.remote_addr,
            'request_id': g.request_id
        }
        log_user_action('http_request', request_info)

@app.after_request
def log_response_info(response):
    """记录响应信息"""
    if hasattr(g, 'start_time'):
        duration = (datetime.now() - g.start_time).total_seconds()
        
        # 只记录错误响应或长时间请求
        if response.status_code >= 400 or duration > 5.0:
            response_info = {
                'status_code': response.status_code,
                'duration': duration,
                'request_id': g.request_id
            }
            log_user_action('http_response', response_info)
    
    return response


# 确保会话报告目录存在
SESSION_REPORTS_DIR = 'session_reports'
os.makedirs(SESSION_REPORTS_DIR, exist_ok=True)

# 下载链接有效期（天数）
DOWNLOAD_LINK_EXPIRE_DAYS = 7

# Dify API配置
DIFY_API_BASE_URL = 'http://*************:81/v1'  # 您的Dify服务器API地址
DIFY_API_KEY = 'app-tRa2x4f3Nq0KZ1TEKDjn0NRs'  # 您的API Key
DIFY_APP_TYPE = 'chatbot'  # 应用类型：聊天助手

# 会话报告存储：session_id -> 报告数据
SESSION_REPORTS = {}


# 启动时清理过期会话
def cleanup_expired_sessions():
    """清理过期的会话数据"""
    try:
        current_time = datetime.now()
        expired_sessions = []

        # 清理内存中的过期会话
        for session_id, session_data in list(SESSION_REPORTS.items()):
            if 'created_at' in session_data:
                created_at = session_data['created_at']
                if current_time - created_at > timedelta(days=DOWNLOAD_LINK_EXPIRE_DAYS):
                    expired_sessions.append(session_id)

        for session_id in expired_sessions:
            del SESSION_REPORTS[session_id]

        # 清理文件系统中的过期会话
        if os.path.exists(SESSION_REPORTS_DIR):
            for filename in os.listdir(SESSION_REPORTS_DIR):
                if filename.endswith('.json'):
                    filepath = os.path.join(SESSION_REPORTS_DIR, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        if 'created_at' in data:
                            created_at = datetime.fromisoformat(data['created_at'])
                            if current_time - created_at > timedelta(days=DOWNLOAD_LINK_EXPIRE_DAYS):
                                os.remove(filepath)
                    except Exception as e:
                        logger.error(f"清理会话文件 {filename} 时出错: {e}")

        if expired_sessions:
            log_business_operation('cleanup_expired_sessions', {'count': len(expired_sessions)})

    except Exception as e:
        logger.error(f"清理过期会话时出错: {e}")
        log_error_operation('cleanup_expired_sessions', e)


# 清理过期会话
cleanup_expired_sessions()


def flatten_json_data(data, prefix=""):
    """扁平化JSON数据，提取所有键值对，包括数组中的数据"""
    flattened = {}

    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, dict):
                # 递归处理嵌套的字典
                flattened.update(flatten_json_data(value, prefix))
            elif isinstance(value, list):
                # 处理数组：遍历数组中的每个元素
                for item in value:
                    if isinstance(item, dict):
                        flattened.update(flatten_json_data(item, prefix))
            else:
                # 检查键是否是占位符格式
                if key.startswith("${") and key.endswith("}"):
                    flattened[key] = str(value)
                else:
                    # 如果不是占位符格式，也添加占位符格式的版本
                    placeholder_key = f"${{{key}}}"
                    flattened[placeholder_key] = str(value)
    elif isinstance(data, list):
        # 如果顶层就是数组，处理数组中的每个元素
        for item in data:
            if isinstance(item, dict):
                flattened.update(flatten_json_data(item, prefix))

    return flattened


def call_dify_api(message, conversation_id=None):
    """调用Dify聊天助手API"""
    try:
        headers = {'Authorization': f'Bearer {DIFY_API_KEY}', 'Content-Type': 'application/json'}

        payload = {'query': message, 'response_mode': 'blocking', 'user': 'user-' + str(uuid.uuid4())[:8], 'inputs': {}}

        if conversation_id:
            payload['conversation_id'] = conversation_id

        response = requests.post(f'{DIFY_API_BASE_URL}/chat-messages', headers=headers, json=payload, timeout=120)  # 2分钟超时

        if response.status_code == 200:
            result = response.json()
            return result
        else:
            error_msg = f"Dify API调用失败: {response.status_code}, {response.text}"
            log_api_call('dify_chat', payload, None, error_msg)
            return None

    except Exception as e:
        log_api_call('dify_chat', None, None, e)
        return None


def generate_initial_report_with_ai(form_data, ai_requirements=""):
    """使用AI生成初始8D报告"""
    try:
        # 构建给AI的消息，包含表单数据和修改意见
        message_parts = []

        # # 添加基本指令
        # message_parts.append("请根据以下8D报告表单数据，生成完善的8D报告。请以JSON格式返回完整的D0-D8数据结构。")

        # 添加表单数据
        message_parts.append(f"表单数据：\n{json.dumps(form_data, ensure_ascii=False, indent=2)}")

        # 添加修改意见（如果有）
        if ai_requirements and ai_requirements.strip():
            message_parts.append(f"修改意见：{ai_requirements.strip()}")

        # # 添加格式要求
        # message_parts.append("请返回完善后的8D报告JSON数据，格式应该包含D0汇报信息、D1建立小组、D2问题描述、D3临时措施、D4根本原因、D5永久措施、D6措施验证、D7预防措施、D8庆贺团队等完整结构。")

        message = "\n\n".join(message_parts)

        # 调用Dify API生成初始报告
        api_response = call_dify_api(message)

        if api_response and 'answer' in api_response:
            ai_answer = api_response['answer']
            # 尝试从AI响应中提取JSON数据
            try:
                # 查找JSON数据（假设AI会返回JSON格式的数据）
                import re

                json_match = re.search(r'\{.*\}', ai_answer, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    enhanced_data = json.loads(json_str)
                    log_business_operation('ai_generate_report_success', {'form_data_keys': list(form_data.keys())})
                    return enhanced_data
                else:
                    log_business_operation('ai_generate_report_no_json', {'ai_response': ai_answer})
                    return form_data
            except json.JSONDecodeError as e:
                log_error_operation('ai_parse_json', e, {'ai_response': ai_answer})
                return form_data
        else:
            # 如果AI调用失败，返回原始表单数据
            log_business_operation('ai_generate_report_failed', {'reason': 'no_api_response'})
            return form_data

    except Exception as e:
        log_error_operation('ai_generate_report', e)
        return form_data


def refine_report_with_ai(current_report, user_feedback, conversation_id=None):
    """使用AI根据用户反馈完善报告"""
    try:
        # 构建给AI的消息
        message = f"""请根据用户反馈修改以下8D报告数据。

当前报告数据：
{json.dumps(current_report, ensure_ascii=False, indent=2)}

用户反馈：
{user_feedback}

请根据反馈修改报告，并以JSON格式返回完整的修改后的D0-D8数据结构。"""

        # 调用Dify API完善报告
        api_response = call_dify_api(message, conversation_id)

        if api_response and 'answer' in api_response:
            ai_answer = api_response['answer']
            conversation_id = api_response.get('conversation_id', conversation_id)

            # 尝试从AI响应中提取JSON数据
            try:
                import re

                json_match = re.search(r'\{.*\}', ai_answer, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    enhanced_data = json.loads(json_str)
                    log_business_operation('ai_refine_report_success', {'conversation_id': conversation_id})
                    return {'report_data': enhanced_data, 'conversation_id': conversation_id, 'ai_response': ai_answer}
                else:
                    # 如果没有找到JSON，返回AI的文本回复和原始数据
                    logger.warning("AI完善报告时未找到JSON数据")
                    log_business_operation('ai_refine_report_no_json', {'conversation_id': conversation_id, 'ai_response': ai_answer})
                    return {'report_data': current_report, 'conversation_id': conversation_id, 'ai_response': ai_answer}
            except json.JSONDecodeError as e:
                logger.error(f"解析AI返回的JSON数据失败: {e}")
                log_error_operation('ai_refine_parse_json', e, {'conversation_id': conversation_id})
                return {'report_data': current_report, 'conversation_id': conversation_id, 'ai_response': ai_answer}
        else:
            logger.warning("AI完善报告失败，API无响应")
            log_business_operation('ai_refine_report_failed', {'conversation_id': conversation_id})
            return {'report_data': current_report, 'conversation_id': conversation_id, 'ai_response': '抱歉，AI服务暂时不可用，请稍后再试。'}

    except Exception as e:
        logger.error(f"AI完善报告时出错: {str(e)}")
        log_error_operation('ai_refine_report', e, {'conversation_id': conversation_id})
        return {'report_data': current_report, 'conversation_id': conversation_id, 'ai_response': f'处理出错: {str(e)}'}


def save_session_report(session_id, report_data):
    """保存会话报告数据"""
    try:
        SESSION_REPORTS[session_id] = {'report_data': report_data, 'created_at': datetime.now(), 'updated_at': datetime.now()}

        # 同时保存到文件系统
        filepath = os.path.join(SESSION_REPORTS_DIR, f"{session_id}.json")
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump({'report_data': report_data, 'created_at': datetime.now().isoformat(), 'updated_at': datetime.now().isoformat()}, f, ensure_ascii=False, indent=2)

        logger.info(f"会话报告保存成功: {session_id}")
        log_business_operation('save_session_report', {'session_id': session_id})
        return True
    except Exception as e:
        logger.error(f"保存会话报告失败: {str(e)}")
        log_error_operation('save_session_report', e, {'session_id': session_id})
        return False


def get_session_report(session_id):
    """获取会话报告数据"""
    try:
        # 先从内存中查找
        if session_id in SESSION_REPORTS:
            return SESSION_REPORTS[session_id]['report_data']

        # 从文件系统中加载
        filepath = os.path.join(SESSION_REPORTS_DIR, f"{session_id}.json")
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data['report_data']

        return None
    except Exception as e:
        logger.error(f"获取会话报告失败: {str(e)}")
        log_error_operation('get_session_report', e, {'session_id': session_id})
        return None


def copy_font_format(source_font, target_font):
    """复制字体格式"""
    try:
        if hasattr(source_font, 'name'):
            target_font.name = source_font.name
        if hasattr(source_font, 'size'):
            target_font.size = source_font.size
        if hasattr(source_font, 'bold'):
            target_font.bold = source_font.bold
        if hasattr(source_font, 'italic'):
            target_font.italic = source_font.italic
        if hasattr(source_font, 'underline'):
            target_font.underline = source_font.underline

        # 安全地复制颜色格式
        if hasattr(source_font, 'color') and source_font.color:
            try:
                # 检查颜色是否有rgb属性
                if hasattr(source_font.color, 'rgb') and source_font.color.rgb:
                    target_font.color.rgb = source_font.color.rgb
                # 对于预设颜色(_PrstColor)，尝试复制颜色类型
                elif hasattr(source_font.color, 'type'):
                    # 只有当目标字体的颜色也支持相同的颜色类型时才复制
                    if hasattr(target_font.color, 'type'):
                        target_font.color.type = source_font.color.type
                    # 对于scheme colors，尝试复制theme_color
                    if hasattr(source_font.color, 'theme_color') and hasattr(target_font.color, 'theme_color'):
                        target_font.color.theme_color = source_font.color.theme_color
            except AttributeError:
                # 如果颜色类型不兼容，跳过颜色复制
                pass
    except Exception as e:
        logger.warning(f"复制字体格式时出现警告: {str(e)}")


def copy_paragraph_format(source_paragraph, target_paragraph):
    """复制段落格式"""
    try:
        if hasattr(source_paragraph, 'alignment'):
            target_paragraph.alignment = source_paragraph.alignment
        if hasattr(source_paragraph, 'space_before'):
            target_paragraph.space_before = source_paragraph.space_before
        if hasattr(source_paragraph, 'space_after'):
            target_paragraph.space_after = source_paragraph.space_after
        if hasattr(source_paragraph, 'line_spacing'):
            target_paragraph.line_spacing = source_paragraph.line_spacing
    except Exception as e:
        logger.warning(f"复制段落格式时出现警告: {str(e)}")


def find_placeholders_in_shape(shape):
    """查找形状中的所有占位符"""
    placeholders = set()

    def extract_from_text(text):
        """从文本中提取占位符"""
        start = 0
        while True:
            start = text.find("${", start)
            if start == -1:
                break
            end = text.find("}", start)
            if end == -1:
                break
            placeholders.add(text[start : end + 1])
            start = end + 1

    if hasattr(shape, "table"):
        # 处理表格中的占位符
        for row in shape.table.rows:
            for cell in row.cells:
                if hasattr(cell, "text"):
                    extract_from_text(cell.text)
    elif hasattr(shape, "text"):
        # 处理普通文本中的占位符
        extract_from_text(shape.text)

    return placeholders


def find_all_placeholders_in_slide(slide):
    """查找幻灯片中的所有占位符"""
    all_placeholders = set()
    for shape in slide.shapes:
        all_placeholders.update(find_placeholders_in_shape(shape))
    return all_placeholders


def process_table_cell(cell, replacements):
    """处理表格单元格中的文本替换"""
    if not hasattr(cell, "text_frame"):
        return False

    original_text = cell.text
    modified_text = original_text
    has_replacement = False

    # 保存原始格式
    original_runs = []
    try:
        for paragraph in cell.text_frame.paragraphs:
            for run in paragraph.runs:
                original_runs.append({'text': run.text, 'font': run.font, 'paragraph': paragraph})
    except Exception:
        pass

    # 执行所有可能的替换
    for key, value in replacements.items():
        if key in modified_text:
            modified_text = modified_text.replace(key, value)
            has_replacement = True

    # 删除未替换的占位符
    while True:
        start = modified_text.find("${")
        if start == -1:
            break
        end = modified_text.find("}", start)
        if end == -1:
            break
        # 删除占位符
        placeholder = modified_text[start : end + 1]
        modified_text = modified_text.replace(placeholder, "")
        has_replacement = True

    # 如果文本有变化，应用新文本并保持格式
    if modified_text != original_text:
        try:
            text_frame = cell.text_frame
            text_frame.clear()
            p = text_frame.paragraphs[0]

            if original_runs:
                run = p.add_run()
                run.text = modified_text
                copy_font_format(original_runs[0]['font'], run.font)
                copy_paragraph_format(original_runs[0]['paragraph'], p)
            else:
                p.text = modified_text
        except Exception:
            pass

    return has_replacement


def process_table(table, replacements):
    """处理表格中的所有单元格"""
    has_replacement = False
    for row in table.rows:
        for cell in row.cells:
            if process_table_cell(cell, replacements):
                has_replacement = True
    return has_replacement


def process_shape_text(shape, replacements):
    """处理形状中的文本替换"""
    has_replacement = False

    # 1. 处理表格
    if hasattr(shape, "table"):
        return process_table(shape.table, replacements)

    # 2. 处理组合形状(Group) - 递归处理子形状
    if hasattr(shape, "shapes"):
        for sub_shape in shape.shapes:
            if process_shape_text(sub_shape, replacements):
                has_replacement = True
        return has_replacement

    # 3. 处理图表中的文本
    if hasattr(shape, "chart"):
        if process_chart_text(shape.chart, replacements):
            has_replacement = True

    # 4. 处理文本框和其他有text_frame的形状
    if hasattr(shape, "text_frame") and shape.text_frame:
        if process_text_frame(shape.text_frame, replacements):
            has_replacement = True

    # 5. 处理普通文本形状（向后兼容）
    elif hasattr(shape, "text") and shape.text:
        original_text = shape.text
        modified_text = original_text

        # 执行所有可能的替换
        for key, value in replacements.items():
            if key in modified_text:
                modified_text = modified_text.replace(key, value)
                has_replacement = True

        # 删除未替换的占位符
        while True:
            start = modified_text.find("${")
            if start == -1:
                break
            end = modified_text.find("}", start)
            if end == -1:
                break
            placeholder = modified_text[start : end + 1]
            modified_text = modified_text.replace(placeholder, "")
            has_replacement = True

        # 如果文本有变化，应用新文本并保持格式
        if modified_text != original_text:
            try:
                if hasattr(shape, 'text_frame') and shape.text_frame:
                    process_text_frame_with_text(shape.text_frame, modified_text)
                else:
                    shape.text = modified_text
            except Exception as e:
                pass  # 静默处理格式错误

    return has_replacement


def process_text_frame(text_frame, replacements):
    """处理text_frame中的文本替换"""
    has_replacement = False

    try:
        for paragraph in text_frame.paragraphs:
            # 获取段落的完整文本
            original_text = paragraph.text
            if not original_text:
                continue

            modified_text = original_text

            # 执行所有可能的替换
            for key, value in replacements.items():
                if key in modified_text:
                    modified_text = modified_text.replace(key, value)
                    has_replacement = True

            # 删除未替换的占位符
            while True:
                start = modified_text.find("${")
                if start == -1:
                    break
                end = modified_text.find("}", start)
                if end == -1:
                    break
                placeholder = modified_text[start : end + 1]
                modified_text = modified_text.replace(placeholder, "")
                has_replacement = True

            # 如果文本有变化，更新段落
            if modified_text != original_text:
                # 保存原始格式信息
                original_runs = []
                for run in paragraph.runs:
                    original_runs.append({'text': run.text, 'font': run.font})

                # 清除段落内容
                paragraph.clear()

                # 添加新内容并保持格式
                if original_runs and modified_text:
                    run = paragraph.add_run()
                    run.text = modified_text
                    try:
                        copy_font_format(original_runs[0]['font'], run.font)
                    except Exception:
                        pass
                elif modified_text:
                    paragraph.add_run().text = modified_text

    except Exception as e:
        pass  # 静默处理错误

    return has_replacement


def process_text_frame_with_text(text_frame, new_text):
    """使用新文本更新text_frame，保持格式"""
    try:
        # 保存第一个段落的格式
        original_runs = []
        if text_frame.paragraphs:
            for run in text_frame.paragraphs[0].runs:
                original_runs.append({'font': run.font})

        # 清除内容
        text_frame.clear()

        # 添加新内容
        if new_text:
            p = text_frame.paragraphs[0]
            run = p.add_run()
            run.text = new_text

            # 尝试恢复格式
            if original_runs:
                try:
                    copy_font_format(original_runs[0]['font'], run.font)
                except Exception:
                    pass

    except Exception as e:
        logger.warning(f"更新text_frame文本时出错: {e}")


def process_chart_text(chart, replacements):
    """处理图表中的文本替换"""
    has_replacement = False

    try:
        # 处理图表标题
        if hasattr(chart, 'chart_title') and chart.chart_title and hasattr(chart.chart_title, 'text_frame'):
            if process_text_frame(chart.chart_title.text_frame, replacements):
                has_replacement = True

        # 处理坐标轴标签
        if hasattr(chart, 'axes'):
            for axis in chart.axes:
                if hasattr(axis, 'axis_title') and axis.axis_title and hasattr(axis.axis_title, 'text_frame'):
                    if process_text_frame(axis.axis_title.text_frame, replacements):
                        has_replacement = True

        # 处理数据标签（这个比较复杂，暂时跳过具体实现）
        # TODO: 如果需要处理数据标签，可以在这里添加

    except Exception as e:
        pass  # 静默处理错误

    return has_replacement


def process_slide(slide, replacements):
    """处理单个幻灯片"""
    for shape in slide.shapes:
        process_shape_text(shape, replacements)


def process_all_slides_in_ppt(prs, replacements):
    """处理PPT中的所有幻灯片"""
    # 收集所有未匹配的占位符
    all_unmatched = set()

    for slide_idx, slide in enumerate(prs.slides):
        # 查找当前页面的所有占位符
        placeholders = find_all_placeholders_in_slide(slide)

        # 检查哪些占位符没有匹配的数据
        for placeholder in placeholders:
            if placeholder not in replacements or not replacements[placeholder]:
                all_unmatched.add(placeholder)

        # 处理当前页面
        process_slide(slide, replacements)

    # 记录未匹配的占位符
    if all_unmatched:
        log_business_operation('ppt_unmatched_placeholders', {'count': len(all_unmatched), 'placeholders': list(all_unmatched)})

    log_business_operation('ppt_processing_complete', {'total_placeholders': len(replacements), 'unmatched_count': len(all_unmatched)})


# DOCX 处理相关函数
def find_all_tables_in_docx(doc):
    """查找docx文档中的所有表格（包括嵌套表格）"""
    all_tables = []

    # 添加主表格
    all_tables.extend(doc.tables)

    # 查找嵌套表格
    def find_nested_tables(tables):
        nested = []
        for table in tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.tables:
                        nested.extend(cell.tables)
                        # 递归查找更深层的嵌套
                        nested.extend(find_nested_tables(cell.tables))
        return nested

    # 递归查找所有嵌套表格
    nested_tables = find_nested_tables(doc.tables)
    all_tables.extend(nested_tables)

    return all_tables


def process_docx_table_cell(cell, replacements):
    """处理docx表格单元格中的文本替换"""
    has_replacement = False

    for para_idx, paragraph in enumerate(cell.paragraphs):
        original_text = paragraph.text

        modified_text = original_text

        # 执行所有可能的替换
        for key, value in replacements.items():
            if key in modified_text:
                modified_text = modified_text.replace(key, value)
                has_replacement = True

        # 删除未替换的占位符
        while True:
            start = modified_text.find("${")
            if start == -1:
                break
            end = modified_text.find("}", start)
            if end == -1:
                break
            # 删除占位符
            placeholder = modified_text[start : end + 1]
            modified_text = modified_text.replace(placeholder, "")
            has_replacement = True

        # 如果文本有变化，更新段落文本
        if modified_text != original_text:
            # 保存原始格式
            runs_data = []
            for run in paragraph.runs:
                runs_data.append({'text': run.text, 'bold': run.bold, 'italic': run.italic, 'underline': run.underline, 'font_name': run.font.name, 'font_size': run.font.size})

            # 清除段落内容
            paragraph.clear()

            # 添加新内容并尝试保持格式
            if runs_data:
                new_run = paragraph.add_run(modified_text)
                try:
                    new_run.bold = runs_data[0]['bold']
                    new_run.italic = runs_data[0]['italic']
                    new_run.underline = runs_data[0]['underline']
                    if runs_data[0]['font_name']:
                        new_run.font.name = runs_data[0]['font_name']
                    if runs_data[0]['font_size']:
                        new_run.font.size = runs_data[0]['font_size']
                except Exception:
                    pass
            else:
                paragraph.add_run(modified_text)

    return has_replacement


def process_docx_table(table, replacements, table_idx):
    """处理docx表格中的所有单元格"""
    has_replacement = False
    for row_idx, row in enumerate(table.rows):
        for cell_idx, cell in enumerate(row.cells):
            cell_text = cell.text.strip()
            if cell_text and ("${" in cell_text):  # 只处理包含占位符的单元格
                if process_docx_table_cell(cell, replacements):
                    has_replacement = True

    return has_replacement


def process_docx_paragraphs(doc, replacements):
    """处理docx文档中的所有段落"""
    has_replacement = False

    for para_idx, paragraph in enumerate(doc.paragraphs):
        original_text = paragraph.text
        if original_text.strip() and ("${" in original_text):  # 只处理包含占位符的段落
            modified_text = original_text

            # 执行所有可能的替换
            for key, value in replacements.items():
                if key in modified_text:
                    modified_text = modified_text.replace(key, value)
                    has_replacement = True

            # 删除未替换的占位符
            while True:
                start = modified_text.find("${")
                if start == -1:
                    break
                end = modified_text.find("}", start)
                if end == -1:
                    break
                placeholder = modified_text[start : end + 1]
                modified_text = modified_text.replace(placeholder, "")
                has_replacement = True

            # 如果文本有变化，更新段落文本
            if modified_text != original_text:
                # 保存原始格式
                runs_data = []
                for run in paragraph.runs:
                    runs_data.append({'text': run.text, 'bold': run.bold, 'italic': run.italic, 'underline': run.underline, 'font_name': run.font.name, 'font_size': run.font.size})

                # 清除段落内容
                paragraph.clear()

                # 添加新内容并尝试保持格式
                if runs_data:
                    new_run = paragraph.add_run(modified_text)
                    try:
                        new_run.bold = runs_data[0]['bold']
                        new_run.italic = runs_data[0]['italic']
                        new_run.underline = runs_data[0]['underline']
                        if runs_data[0]['font_name']:
                            new_run.font.name = runs_data[0]['font_name']
                        if runs_data[0]['font_size']:
                            new_run.font.size = runs_data[0]['font_size']
                    except Exception:
                        pass
                else:
                    paragraph.add_run(modified_text)

    return has_replacement


def process_all_content_in_docx(doc, replacements):
    """处理DOCX中的所有内容"""
    # 收集所有未匹配的占位符
    all_unmatched = set()

    # 查找文档中的所有占位符
    for paragraph in doc.paragraphs:
        text = paragraph.text
        if "${" in text:
            # 提取占位符
            import re

            placeholders = re.findall(r'\$\{[^}]+\}', text)
            for placeholder in placeholders:
                if placeholder not in replacements or not replacements[placeholder]:
                    all_unmatched.add(placeholder)

    # 查找表格中的占位符
    all_tables = find_all_tables_in_docx(doc)
    for table in all_tables:
        for row in table.rows:
            for cell in row.cells:
                text = cell.text
                if "${" in text:
                    import re

                    placeholders = re.findall(r'\$\{[^}]+\}', text)
                    for placeholder in placeholders:
                        if placeholder not in replacements or not replacements[placeholder]:
                            all_unmatched.add(placeholder)

    # 处理所有段落
    process_docx_paragraphs(doc, replacements)

    # 处理所有表格
    for table_idx, table in enumerate(all_tables):
        process_docx_table(table, replacements, table_idx)

    # 记录未匹配的占位符
    if all_unmatched:
        log_business_operation('docx_unmatched_placeholders', {'count': len(all_unmatched), 'placeholders': list(all_unmatched)})

    log_business_operation('docx_processing_complete', {'total_placeholders': len(replacements), 'unmatched_count': len(all_unmatched)})


# 路由处理函数
@app.route('/process_ppt', methods=['POST'])
def process_ppt():
    try:
        data = request.get_json()

        if not data or 'template_name' not in data or 'replacements' not in data:
            return jsonify({'error': '无效的请求数据'}), 400

        template_name = data['template_name']
        replacements_data = data['replacements']

        if not os.path.exists(template_name):
            return jsonify({'error': f'模板文件 {template_name} 不存在'}), 404

        # 加载PPT模板
        prs = Presentation(template_name)

        # 扁平化所有替换数据
        all_replacements = flatten_json_data(replacements_data)

        # 处理整个PPT
        process_all_slides_in_ppt(prs, all_replacements)

        # 生成唯一的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = str(uuid.uuid4())[:8]
        output_filename = f"{timestamp}_{random_suffix}.pptx"

        # 使用内存中的BytesIO对象而不是保存到磁盘
        output_buffer = BytesIO()
        prs.save(output_buffer)
        output_buffer.seek(0)

        # 直接返回文件流
        return send_file(output_buffer, as_attachment=True, download_name=output_filename, mimetype='application/vnd.openxmlformats-officedocument.presentationml.presentation')

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/process_docx', methods=['POST'])
def process_docx():
    try:
        data = request.get_json()

        if not data or 'template_name' not in data or 'replacements' not in data:
            return jsonify({'error': '无效的请求数据'}), 400

        template_name = data['template_name']
        replacements_data = data['replacements']

        if not os.path.exists(template_name):
            return jsonify({'error': f'模板文件 {template_name} 不存在'}), 404

        # 加载DOCX模板
        doc = Document(template_name)

        # 扁平化所有替换数据
        all_replacements = flatten_json_data(replacements_data)

        # 处理整个DOCX文档
        process_all_content_in_docx(doc, all_replacements)

        # 生成唯一的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = str(uuid.uuid4())[:8]
        output_filename = f"{timestamp}_{random_suffix}.docx"

        # 使用内存中的BytesIO对象而不是保存到磁盘
        output_buffer = BytesIO()
        doc.save(output_buffer)
        output_buffer.seek(0)
        logger.info(f"文件已生成: {output_filename}")
        log_file_operation('generate_docx', output_filename, {'template': template_name})

        # 直接返回文件流
        return send_file(output_buffer, as_attachment=True, download_name=output_filename, mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document')

    except Exception as e:
        logger.error(f"处理DOCX时出错: {str(e)}")
        log_error_operation('process_docx', e, {'template_name': template_name})
        import traceback

        traceback.print_exc()
        return jsonify({'error': str(e)}), 500


@app.route('/')
def index():
    """主页 - 显示8D报告表单"""
    try:
        # 获取flash消息
        return render_template('d8_form.html')
    except Exception as e:
        return f"页面加载失败: {str(e)}", 500


@app.route('/submit_8d_report', methods=['POST'])
def submit_8d_report():
    """处理8D报告表单提交，返回JSON响应并启动AI协作流程"""
    try:
        # 只处理新的JSON格式请求
        raw_data = request.get_data(as_text=True)

        # 检查是否包含修改意见
        ai_requirements = ""
        if raw_data.startswith('修改意见：'):
            # 解析修改意见和表单数据
            parts = raw_data.split('\n\n表单数据：', 1)
            if len(parts) == 2:
                ai_requirements = parts[0].replace('修改意见：', '').strip()
                form_data = json.loads(parts[1])
            else:
                raise ValueError("请求格式错误：无法解析修改意见和表单数据")
        else:
            # 直接解析JSON（没有修改意见）
            form_data = json.loads(raw_data)

        # 记录提交的D0-D8数据
        if ai_requirements:
            pass
        
        log_business_operation('submit_8d_report', {
            'form_data_keys': list(form_data.keys()),
            'has_ai_requirements': bool(ai_requirements)
        })

        # 生成会话ID
        session_id = str(uuid.uuid4())

        # 使用AI生成初始报告（如果配置了Dify API）
        enhanced_data = form_data  # 默认使用原始数据
        if DIFY_API_KEY and DIFY_API_KEY.startswith('app-'):
            logger.info("正在使用AI生成初始报告...")
            try:
                enhanced_data = generate_initial_report_with_ai(form_data, ai_requirements)
                if enhanced_data != form_data:

                    log_business_operation('ai_enhancement_success', {'session_id': session_id})
            except Exception as ai_error:
                logger.error(f"AI生成失败，使用原始数据: {ai_error}")
                log_error_operation('ai_generation_failed', ai_error, {'session_id': session_id})
        else:
            logger.info("未配置AI服务，使用原始表单数据")
            log_business_operation('no_ai_service', {'session_id': session_id})

        # 保存会话报告
        save_session_report(session_id, enhanced_data)

        # 返回JSON响应
        return jsonify({'status': 'success', 'message': 'AI正在生成初始报告...', 'session_id': session_id, 'enhanced_data': enhanced_data if enhanced_data != form_data else None})

    except Exception as e:
        error_msg = f"处理失败: {str(e)}"
        logger.error(error_msg)
        log_error_operation('submit_8d_report', e)

        return jsonify({'status': 'error', 'error': error_msg}), 500


@app.route('/generate_direct_report', methods=['POST'])
def generate_direct_report():
    """直接生成8D报告，不使用AI优化"""
    try:
        # 只处理新的JSON格式请求
        raw_data = request.get_data(as_text=True)

        # 对于直接生成报告，可能不包含修改意见，直接解析JSON
        try:
            form_data = json.loads(raw_data)
        except json.JSONDecodeError:
            # 如果解析失败，可能是包含修改意见的格式，但对于直接生成我们忽略修改意见
            if raw_data.startswith('修改意见：'):
                parts = raw_data.split('\n\n表单数据：', 1)
                if len(parts) == 2:
                    form_data = json.loads(parts[1])
                else:
                    raise ValueError("无法解析请求数据")
            else:
                raise ValueError("无法解析请求数据")

        # 生成会话ID
        session_id = str(uuid.uuid4())

        # 直接使用原始表单数据，不进行AI优化
        
        log_business_operation('generate_direct_report', {
            'session_id': session_id,
            'form_data_keys': list(form_data.keys())
        })

        # 保存会话报告
        save_session_report(session_id, form_data)

        # 直接使用表单数据作为替换数据
        replacements_data = form_data

        # 生成PPT和DOCX文件
        try:
            # 选择模板版本（0530 或 0610）
            template_version = '0530'  # 默认使用0530模板
            template_name = f"8d_template/{template_version}/template.pptx"
            docx_template_name = f"8d_template/{template_version}/template.docx"

            # 如果模板不存在，回退到根目录
            if not os.path.exists(template_name):
                template_name = "template.pptx"
            if not os.path.exists(docx_template_name):
                docx_template_name = "template.docx"

            # 创建内存中的ZIP文件
            zip_buffer = BytesIO()

            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                # 生成PPT
                if os.path.exists(template_name):
                    prs = Presentation(template_name)
                    all_replacements = flatten_json_data(replacements_data)
                    process_all_slides_in_ppt(prs, all_replacements)

                    # 生成唯一的文件名
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    random_suffix = str(uuid.uuid4())[:8]
                    ppt_filename = f"{timestamp}_{random_suffix}.pptx"

                    # 保存PPT到内存
                    ppt_buffer = BytesIO()
                    prs.save(ppt_buffer)
                    ppt_buffer.seek(0)

                    # 添加到ZIP
                    zip_file.writestr(ppt_filename, ppt_buffer.getvalue())

                # 生成DOCX
                if os.path.exists(docx_template_name):
                    doc = Document(docx_template_name)
                    all_replacements = flatten_json_data(replacements_data)
                    process_all_content_in_docx(doc, all_replacements)

                    # 生成唯一的文件名
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    random_suffix = str(uuid.uuid4())[:8]
                    docx_filename = f"{timestamp}_{random_suffix}.docx"

                    # 保存DOCX到内存
                    docx_buffer = BytesIO()
                    doc.save(docx_buffer)
                    docx_buffer.seek(0)

                    # 添加到ZIP
                    zip_file.writestr(docx_filename, docx_buffer.getvalue())

            zip_buffer.seek(0)

            # 生成ZIP文件名
            zip_filename = f"8D_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

            # 直接返回ZIP文件
            return send_file(zip_buffer, as_attachment=True, download_name=zip_filename, mimetype='application/zip')

        except Exception as gen_error:
            logger.error(f"文件生成出错: {gen_error}")
            log_error_operation('generate_files', gen_error, {'session_id': session_id})
            return jsonify({'status': 'error', 'error': f"文件生成失败: {str(gen_error)}"}), 500

    except Exception as e:
        error_msg = f"直接生成报告失败: {str(e)}"
        logger.error(error_msg)
        log_error_operation('generate_direct_report', e)
        return jsonify({'status': 'error', 'error': error_msg}), 500


@app.route('/submit_8d_report_original', methods=['POST'])
def submit_8d_report_original():
    """原始的8D报告表单提交处理（保留作为备用）"""
    try:
        # 收集表单数据
        form_data = request.form.to_dict()

        # 定义所有可能的表单字段映射到JSON占位符
        # 这确保即使字段为空也会在JSON中显示
        base_field_mapping = {
            # D0 基本信息
            'd0_title': '${D0标题}',
            'd0_reporter': '${D0汇报人}',
            'd0_time': '${D0汇报时间}',
            'd0_background': '${D0项目背景}',
            # D1 组长信息
            'd1_leader_name': '${D1组长姓名}',
            'd1_leader_dept': '${D1组长部门}',
            'd1_leader_position': '${D1组长职位}',
            'd1_leader_responsibility': '${D1组长主要职责}',
            # D2 问题描述
            'd2_description': '${D2事件整体描述}',
            'd2_when': '${D2何时发生}',
            'd2_where': '${D2何地发生}',
            'd2_who': '${D2何人发现}',
            'd2_what': '${D2发生了什么问题}',
            'd2_why': '${D2为什么是这问题}',
            'd2_how': '${D2问题如何发生}',
            'd2_impact': '${D2问题影响程度}',
            # D4 5Why分析
            'd4_why1': '${D4why1}',
            'd4_answer1': '${D4answer1}',
            'd4_why2': '${D4why2}',
            'd4_answer2': '${D4answer2}',
            'd4_why3': '${D4why3}',
            'd4_answer3': '${D4answer3}',
            'd4_why4': '${D4why4}',
            'd4_answer4': '${D4answer4}',
            'd4_why5': '${D4why5}',
            'd4_answer5': '${D4answer5}',
            'd4_summary': '${D4原因小结}',
            # D8 庆贺团队
            'd8_effectiveness': '${D8有效性确认}',
            'd8_confirmer': '${D8确认人}',
            'd8_confirm_time': '${D8确认完成时间}',
        }

        # 构建基础JSON数据结构，确保所有基础字段都存在
        json_data = {
            "template_name": "template.pptx",
            "replacements": {
                "D0汇报信息": {
                    base_field_mapping['d0_title']: form_data.get('d0_title', ''),
                    base_field_mapping['d0_reporter']: form_data.get('d0_reporter', ''),
                    base_field_mapping['d0_time']: form_data.get('d0_time', ''),
                    base_field_mapping['d0_background']: form_data.get('d0_background', ''),
                },
                "D1建立小组": {
                    "组长": {
                        base_field_mapping['d1_leader_name']: form_data.get('d1_leader_name', ''),
                        base_field_mapping['d1_leader_dept']: form_data.get('d1_leader_dept', ''),
                        base_field_mapping['d1_leader_position']: form_data.get('d1_leader_position', ''),
                        base_field_mapping['d1_leader_responsibility']: form_data.get('d1_leader_responsibility', ''),
                    }
                },
                "D2问题描述": {
                    base_field_mapping['d2_description']: form_data.get('d2_description', ''),
                    "5W2H": {
                        base_field_mapping['d2_when']: form_data.get('d2_when', ''),
                        base_field_mapping['d2_where']: form_data.get('d2_where', ''),
                        base_field_mapping['d2_who']: form_data.get('d2_who', ''),
                        base_field_mapping['d2_why']: form_data.get('d2_why', ''),
                        base_field_mapping['d2_what']: form_data.get('d2_what', ''),
                        base_field_mapping['d2_how']: form_data.get('d2_how', ''),
                        base_field_mapping['d2_impact']: form_data.get('d2_impact', ''),
                    },
                },
                "D3临时措施": {},
                "D4根本原因": {
                    "5why分析": {
                        base_field_mapping['d4_why1']: form_data.get('d4_why1', ''),
                        base_field_mapping['d4_answer1']: form_data.get('d4_answer1', ''),
                        base_field_mapping['d4_why2']: form_data.get('d4_why2', ''),
                        base_field_mapping['d4_answer2']: form_data.get('d4_answer2', ''),
                        base_field_mapping['d4_why3']: form_data.get('d4_why3', ''),
                        base_field_mapping['d4_answer3']: form_data.get('d4_answer3', ''),
                        base_field_mapping['d4_why4']: form_data.get('d4_why4', ''),
                        base_field_mapping['d4_answer4']: form_data.get('d4_answer4', ''),
                        base_field_mapping['d4_why5']: form_data.get('d4_why5', ''),
                        base_field_mapping['d4_answer5']: form_data.get('d4_answer5', ''),
                    },
                    "人机料法环测分析": {},
                    base_field_mapping['d4_summary']: form_data.get('d4_summary', ''),
                },
                "D5永久措施": {},
                "D6措施验证": {},
                "D7预防措施": {},
                "D8庆贺团队": {
                    base_field_mapping['d8_effectiveness']: form_data.get('d8_effectiveness', ''),
                    base_field_mapping['d8_confirmer']: form_data.get('d8_confirmer', ''),
                    base_field_mapping['d8_confirm_time']: form_data.get('d8_confirm_time', ''),
                },
            },
        }

        # 添加D1成员信息 - 确保至少包含成员1和成员2的空结构
        for member_num in [1, 2]:
            json_data["replacements"]["D1建立小组"][f"成员{member_num}"] = {
                f"${{D1成员{member_num}姓名}}": form_data.get(f'd1_member{member_num}_name', ''),
                f"${{D1成员{member_num}部门}}": form_data.get(f'd1_member{member_num}_dept', ''),
                f"${{D1成员{member_num}职位}}": form_data.get(f'd1_member{member_num}_position', ''),
                f"${{D1成员{member_num}主要职责}}": form_data.get(f'd1_member{member_num}_responsibility', ''),
            }

        # 动态添加额外的成员信息
        member_numbers = set()
        for key in form_data.keys():
            if key.startswith('d1_member') and key.endswith('_name'):
                try:
                    member_num = int(key.split('_')[1].replace('member', ''))
                    if member_num > 2:  # 只处理成员3及以上
                        member_numbers.add(member_num)
                except (IndexError, ValueError):
                    continue

        # 为额外成员添加数据
        for i in sorted(member_numbers):
            json_data["replacements"]["D1建立小组"][f"成员{i}"] = {
                f"${{D1成员{i}姓名}}": form_data.get(f'd1_member{i}_name', ''),
                f"${{D1成员{i}部门}}": form_data.get(f'd1_member{i}_dept', ''),
                f"${{D1成员{i}职位}}": form_data.get(f'd1_member{i}_position', ''),
                f"${{D1成员{i}主要职责}}": form_data.get(f'd1_member{i}_responsibility', ''),
            }

        # 添加D3临时措施信息 - 确保至少包含措施1的空结构
        json_data["replacements"]["D3临时措施"]["临时措施1"] = {
            "${D3范围1}": form_data.get('d3_scope1', ''),
            "${D3处置对策1}": form_data.get('d3_measure1', ''),
            "${D3责任人1}": form_data.get('d3_responsible1', ''),
            "${D3完成期限1}": form_data.get('d3_deadline1', ''),
            "${D3状态1}": form_data.get('d3_status1', ''),
            "${D3进度备注1}": form_data.get('d3_note1', ''),
        }

        # 动态添加额外的D3措施
        measure_numbers = set()
        for key in form_data.keys():
            if key.startswith('d3_scope') and key[8:].isdigit():
                try:
                    measure_num = int(key[8:])
                    if measure_num > 1:  # 只处理措施2及以上
                        measure_numbers.add(measure_num)
                except ValueError:
                    continue

        for i in sorted(measure_numbers):
            json_data["replacements"]["D3临时措施"][f"临时措施{i}"] = {
                f"${{D3范围{i}}}": form_data.get(f'd3_scope{i}', ''),
                f"${{D3处置对策{i}}}": form_data.get(f'd3_measure{i}', ''),
                f"${{D3责任人{i}}}": form_data.get(f'd3_responsible{i}', ''),
                f"${{D3完成期限{i}}}": form_data.get(f'd3_deadline{i}', ''),
                f"${{D3状态{i}}}": form_data.get(f'd3_status{i}', ''),
                f"${{D3进度备注{i}}}": form_data.get(f'd3_note{i}', ''),
            }

        # 添加D4人机料法环测原因分析 - 将同类别的原因整合成数组
        cause_categories = {'man': '人原因', 'machine': '机原因', 'material': '料原因', 'method': '法原因', 'environment': '环原因', 'measurement': '测原因'}

        for category, label in cause_categories.items():
            # 收集所有该类别的原因编号
            cause_numbers = set([1])  # 确保至少包含原因1
            for key in form_data.keys():
                if key.startswith(f'd4_{category}') and not key.endswith('_judgment') and not key.endswith('_evidence'):
                    suffix = key[len(f'd4_{category}') :]
                    if suffix.isdigit():
                        try:
                            cause_num = int(suffix)
                            cause_numbers.add(cause_num)
                        except ValueError:
                            continue

            # 构建该类别的所有原因数组
            causes_array = []
            for i in sorted(cause_numbers):
                # 提取类别名称（去掉"原因"）
                category_name = label.replace('原因', '')
                cause_item = {
                    f"${{D4{label}{i}}}": form_data.get(f'd4_{category}{i}', ''),
                    f"${{D4{category_name}判定{i}}}": form_data.get(f'd4_{category}{i}_judgment', ''),
                    f"${{D4{category_name}证据{i}}}": form_data.get(f'd4_{category}{i}_evidence', ''),
                }
                causes_array.append(cause_item)

            # 将整个类别的原因作为一个整体赋值
            json_data["replacements"]["D4根本原因"]["人机料法环测分析"][label] = causes_array

        # 添加D5永久措施信息 - 确保至少包含措施1的空结构
        json_data["replacements"]["D5永久措施"]["措施1"] = {
            "${D5纠正措施1}": form_data.get('d5_measure1', ''),
            "${D5责任人1}": form_data.get('d5_responsible1', ''),
            "${D5计划完成日期1}": form_data.get('d5_deadline1', ''),
        }

        # 动态添加额外的D5措施
        d5_measure_numbers = set()
        for key in form_data.keys():
            if key.startswith('d5_measure') and key[10:].isdigit():
                try:
                    measure_num = int(key[10:])
                    if measure_num > 1:  # 只处理措施2及以上
                        d5_measure_numbers.add(measure_num)
                except ValueError:
                    continue

        for i in sorted(d5_measure_numbers):
            json_data["replacements"]["D5永久措施"][f"措施{i}"] = {
                f"${{D5纠正措施{i}}}": form_data.get(f'd5_measure{i}', ''),
                f"${{D5责任人{i}}}": form_data.get(f'd5_responsible{i}', ''),
                f"${{D5计划完成日期{i}}}": form_data.get(f'd5_deadline{i}', ''),
            }

        # 添加D6措施验证信息 - 确保至少包含验证1的空结构
        json_data["replacements"]["D6措施验证"]["验证1"] = {
            "${D6措施验证1}": form_data.get('d6_verification1', ''),
            "${D6验证人1}": form_data.get('d6_verifier1', ''),
            "${D6验证时间1}": form_data.get('d6_time1', ''),
            "${D6验证结果1}": form_data.get('d6_result1', ''),
        }

        # 动态添加额外的D6验证
        d6_verification_numbers = set()
        for key in form_data.keys():
            if key.startswith('d6_verification') and key[15:].isdigit():
                try:
                    verification_num = int(key[15:])
                    if verification_num > 1:  # 只处理验证2及以上
                        d6_verification_numbers.add(verification_num)
                except ValueError:
                    continue

        for i in sorted(d6_verification_numbers):
            json_data["replacements"]["D6措施验证"][f"验证{i}"] = {
                f"${{D6措施验证{i}}}": form_data.get(f'd6_verification{i}', ''),
                f"${{D6验证人{i}}}": form_data.get(f'd6_verifier{i}', ''),
                f"${{D6验证时间{i}}}": form_data.get(f'd6_time{i}', ''),
                f"${{D6验证结果{i}}}": form_data.get(f'd6_result{i}', ''),
            }

        # 添加D7预防措施信息 - 确保至少包含预防1的空结构
        json_data["replacements"]["D7预防措施"]["预防1"] = {
            "${D7预防措施1}": form_data.get('d7_prevention1', ''),
            "${D7责任人1}": form_data.get('d7_responsible1', ''),
            "${D7计划完成日期1}": form_data.get('d7_deadline1', ''),
        }

        # 动态添加额外的D7预防措施
        d7_prevention_numbers = set()
        for key in form_data.keys():
            if key.startswith('d7_prevention') and key[13:].isdigit():
                try:
                    prevention_num = int(key[13:])
                    if prevention_num > 1:  # 只处理预防措施2及以上
                        d7_prevention_numbers.add(prevention_num)
                except ValueError:
                    continue

        for i in sorted(d7_prevention_numbers):
            json_data["replacements"]["D7预防措施"][f"预防{i}"] = {
                f"${{D7预防措施{i}}}": form_data.get(f'd7_prevention{i}', ''),
                f"${{D7责任人{i}}}": form_data.get(f'd7_responsible{i}', ''),
                f"${{D7计划完成日期{i}}}": form_data.get(f'd7_deadline{i}', ''),
            }

        # 使用flash消息并重定向到主页
        flash("8D报告数据已成功处理！", "success")
        return redirect(url_for('index'))

    except Exception as e:
        # 发生错误时也使用flash消息并重定向
        flash(f"保存失败: {str(e)}", "error")
        return redirect(url_for('index'))


# ==================== 新的协作编辑API端点 ====================

@app.route('/api/logs', methods=['GET'])
def get_logs():
    """获取日志信息（仅用于开发调试）"""
    try:
        log_type = request.args.get('type', 'app')  # app, business, error
        lines = int(request.args.get('lines', 100))
        
        log_file = f'logs/{log_type}.log'
        if not os.path.exists(log_file):
            return jsonify({'error': f'日志文件 {log_type}.log 不存在'}), 404
        
        with open(log_file, 'r', encoding='utf-8') as f:
            log_lines = f.readlines()
        
        # 返回最后N行
        recent_logs = log_lines[-lines:] if len(log_lines) > lines else log_lines
        
        return jsonify({
            'log_type': log_type,
            'total_lines': len(log_lines),
            'returned_lines': len(recent_logs),
            'logs': recent_logs
        })
        
    except Exception as e:
        logger.error(f"获取日志失败: {e}")
        return jsonify({'error': str(e)}), 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5555, debug=True)
